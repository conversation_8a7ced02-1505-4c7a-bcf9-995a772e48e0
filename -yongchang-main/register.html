<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>永昌校友系统 - 注册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(to bottom, #3070FA, #6A5EDF);
            color: #fff;
            line-height: 1.4;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            padding: 12px;
            display: flex;
            flex-direction: column;
        }

        header {
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 12px;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .content {
            background-color: #fff;
            border-radius: 12px;
            padding: 15px;
            flex: 1;
            margin-bottom: 12px;
        }

        .form-title {
            font-size: 16px;
            color: #333;
            text-align: center;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .form-group label span {
            color: #f44336;
        }

        .form-group input[type="text"],
        .form-group input[type="tel"] {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            color: #333;
            background-color: #f8f8f8;
            outline: none;
            box-sizing: border-box;
        }

        .form-group input::placeholder {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .form-group input:focus {
            border-color: #3070FA;
        }

        .radio-group {
            display: flex;
            gap: 12px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            color: #333;
            cursor: pointer;
            font-size: 14px;
            gap: 4px;
        }

        .radio-label input {
            margin-right: 4px;
        }

        .btn {
            background-color: #3070FA;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 0;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 50px;
            width: 100%;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2861e0;
        }

        .login-link {
            text-align: center;
            margin-top: 10px;
            font-size: 13px;
            color: #333;
        }

        .login-link a {
            color: #3070FA;
            text-decoration: none;
            font-weight: 500;
        }

        footer {
            margin-top: auto;
            text-align: center;
            font-size: 12px;
            opacity: 0.7;
            padding: 8px 0;
        }

        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px 16px;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            animation: fadeIn 0.3s;
            max-width: 80%;
            text-align: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .message.fade-out {
            animation: fadeOut 0.3s;
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .message.error {
            background-color: rgba(244, 67, 54, 0.9);
        }

        .message.success {
            background-color: rgba(48, 112, 250, 0.9);
        }

        /* Form layout styles */
        .form-group .flex-container,
        .form-group .radio-group {
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: center;
            gap: 8px;
            vertical-align: middle;
        }
        
        .form-group select {
            margin: 0;
            padding: 6px 10px;
            font-size: 13px;
            flex: 0 0 auto;
        }
        
        /* Responsive adjustments */
        @media (max-width: 480px) {
            .form-group .flex-container,
            .form-group .radio-group {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .form-group select {
                width: 100%;
                margin-bottom: 4px;
            }
            
            .form-group .radio-group {
                gap: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>永昌校友系统</h1>
        </header>

        <div class="content">
            <h2 class="form-title">身份注册</h2>
            <form id="registerForm">

                <!-- Name field remains -->
                <div class="form-group" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 80px; vertical-align: middle; margin-bottom: 0;">姓名 <span>*</span></label>
                    <div style="display: inline-block; vertical-align: middle; width: calc(100% - 85px);">
                        <input type="text" id="name" name="name" placeholder="请输入真实姓名" required>
                    </div>
                </div>

                <!-- Gender field remains -->
                <div class="form-group gender-field" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 80px; vertical-align: middle; margin-bottom: 0;">性别 <span>*</span></label>
                    <div style="display: inline-block; vertical-align: middle;">
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="gender" value="男" required> 男
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="gender" value="女"> 女
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Birth date field with dynamic day selection -->
                <div class="form-group birthdate-field" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 65px; vertical-align: middle; margin-bottom: 0;">出生日期 <span>*</span></label>
                    <div class="flex-container" style="display: inline-flex; flex-wrap: wrap; align-items: center; gap: 8px;">
                        <select name="birthYear" id="birthYear" required>
                            <option value="">请选择年份</option>
                            <!-- Generate years from 1980 to current year -->
                            <script>
                                const currentYear = new Date().getFullYear();
                                for (let i = currentYear; i >= 1980; i--) {
                                    document.write(`<option value="${i}">${i}年</option>`);
                                }
                            </script>
                        </select>
                        
                        <select name="birthMonth" id="birthMonth" required>
                            <option value="">请选择月份</option>
                            <!-- Generate months -->
                            <script>
                                for (let i = 1; i <= 12; i++) {
                                    document.write(`<option value="${i < 10 ? '0' + i : i}">${i < 10 ? '0' + i : i}月</option>`);
                                }
                            </script>
                        </select>
                        
                        <select name="birthDay" id="birthDay" required>
                            <option value="">请选择日期</option>
                        </select>
                    </div>
                    <small id="dateError" class="error-message" style="color: #f44336; font-size: 12px; margin-top: 4px; display: none;">请选择有效的日期</small>
                </div>
    <style>
        /* Form layout styles */
        .form-group .flex-container {
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: center;
            gap: 8px;
            vertical-align: middle;
        }
        
        .form-group select {
            margin: 0;
            padding: 6px 10px;
            font-size: 13px;
            flex: 0 0 auto;
        }
        
        /* Responsive adjustments */
        @media (max-width: 480px) {
            .form-group .flex-container {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .form-group select {
                width: 100%;
                margin-bottom: 4px;
            }
        }
    </style>

                <!-- Education stage field remains with updated options -->
                <div class="form-group education-field" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 80px; vertical-align: middle; margin-bottom: 0;">学段 <span>*</span></label>
                    <div class="flex-container" style="display: inline-flex; flex-wrap: wrap; align-items: center; gap: 12px;">
                        <label class="radio-label">
                            <input type="radio" name="education" value="小学" required> 小学
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="education" value="初中"> 初中
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="education" value="小学&初中"> 小学&初中
                        </label>
                    </div>
                </div>

                <!-- Graduation class field added -->
                <div class="form-group class-field" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 80px; vertical-align: middle; margin-bottom: 0;">毕业班级 <span>*</span></label>
                    <div style="display: inline-block; vertical-align: middle;">
                        <select name="graduationClass" required>
                            <option value="">请选择班级</option>
                            <option value="1班">1班</option>
                            <option value="2班">2班</option>
                            <option value="3班">3班</option>
                            <option value="4班">4班</option>
                        </select>
                    </div>
                </div>

                <!-- Remarks field added -->
                <div class="form-group remarks-field" style="margin-top: 30px;">
                    <label style="display: inline-block; width: 80px; vertical-align: middle; margin-bottom: 0;">备注</label>
                    <div style="display: inline-block; vertical-align: middle; width: calc(100% - 85px);">
                        <input type="text" id="remarks" name="remarks" placeholder="请输入备注信息">
                    </div>
                </div>

                <button type="submit" class="btn">注册</button>

                <div class="login-link">
                    <span>已有账号？</span>
                    <a href="index.html">立即登录</a>
                </div>
            </form>
        </div>

        <footer>
            <p>永昌校友系统 © 2025</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            const nameInput = document.getElementById('name');
            const idcardInput = document.getElementById('idcard');
            const phoneInput = document.getElementById('phone');
            const userTypeInputs = document.querySelectorAll('input[name="userType"]');
            
            // Update validation function to remove user type validation
            function updateDays() {
                const yearSelect = document.getElementById('birthYear');
                const monthSelect = document.getElementById('birthMonth');
                const daySelect = document.getElementById('birthDay');
                const dateError = document.getElementById('dateError');
                
                const year = parseInt(yearSelect.value);
                const month = parseInt(monthSelect.value);
                
                // 如果年份或月份未选择，则不更新天数
                if (isNaN(year) || isNaN(month)) {
                    daySelect.innerHTML = '<option value="">请选择日期</option>';
                    dateError.style.display = 'none';
                    return;
                }

                // 获取该月的最大天数
                const maxDays = new Date(year, month, 0).getDate();
                
                // 更新天数选项
                daySelect.innerHTML = '';
                for (let i = 1; i <= maxDays; i++) {
                    const day = i < 10 ? '0' + i : i;
                    const option = document.createElement('option');
                    option.value = day;
                    option.textContent = day + '日';
                    daySelect.appendChild(option);
                }
                
                // 验证已选日期是否有效
                const selectedDay = parseInt(daySelect.value);
                if (!isNaN(selectedDay) && (selectedDay < 1 || selectedDay > maxDays)) {
                    dateError.style.display = 'block';
                } else {
                    dateError.style.display = 'none';
                }
            }
            
            // 监听年份和月份的变化
            document.getElementById('birthYear').addEventListener('change', updateDays);
            document.getElementById('birthMonth').addEventListener('change', updateDays);
            
            function validateForm() {
                // 姓名验证
                const name = nameInput.value.trim();
                if (!name) {
                    showMessage('请输入姓名', 'error');
                    return false;
                }
                
                // 性别验证
                const gender = document.querySelector('input[name="gender"]:checked');
                if (!gender) {
                    showMessage('请选择性别', 'error');
                    return false;
                }
                
                // 出生日期验证
                const birthYear = document.getElementById('birthYear').value;
                const birthMonth = document.getElementById('birthMonth').value;
                const birthDay = document.getElementById('birthDay').value;
                
                if (!birthYear || !birthMonth || !birthDay) {
                    showMessage('请选择完整的出生日期', 'error');
                    return false;
                }
                
                // 验证日期是否有效
                if (document.getElementById('dateError').style.display === 'block') {
                    showMessage('请选择有效的日期', 'error');
                    return false;
                }
                
                // 学段验证
                const education = document.querySelector('input[name="education"]:checked');
                if (!education) {
                    showMessage('请选择学段', 'error');
                    return false;
                }
                
                // 毕业班级验证
                const graduationClass = document.querySelector('select[name="graduationClass"]').value;
                if (!graduationClass) {
                    showMessage('请选择毕业班级', 'error');
                    return false;
                }
                
                // 备注验证（可选）
                // 不需要验证，因为它是可选项
                
                return true;
            }
            
            function submitForm() {
                const submitBtn = document.querySelector('.btn');
                const originalText = submitBtn.textContent;
                submitBtn.disabled = true;
                submitBtn.textContent = '注册中...';
                
                // Get form data
                const formData = new FormData(registerForm);
                const formObject = {};
                formData.forEach((value, key) => {
                    formObject[key] = value;
                });
                
                // Add birth date field
                const birthYear = document.querySelector('select[name="birthYear"]').value;
                const birthMonth = document.querySelector('select[name="birthMonth"]').value;
                const birthDay = document.querySelector('select[name="birthDay"]').value;
                formObject.birthDate = `${birthYear}-${birthMonth}-${birthDay}`;
                
                // Add graduation class field
                const graduationClass = document.querySelector('select[name="graduationClass"]').value;
                formObject.graduationClass = graduationClass;
                
                // Add remarks field
                const remarks = document.getElementById('remarks').value.trim();
                formObject.remarks = remarks;
                
                // Removed user type related code
                
                // Simulate registration request
                setTimeout(() => {
                    console.log('Registration request:', formObject);
                    
                    // Registration successful
                    showMessage('注册成功，即将跳转到登录页面', 'success');
                    
                    // Delay redirect to login page
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                    
                    // Restore button state
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }, 1500);
            }
            
            function showMessage(message, type) {
                // 移除已有消息
                const oldMessage = document.querySelector('.message');
                if (oldMessage) {
                    oldMessage.remove();
                }
                
                // 创建新消息
                const messageEl = document.createElement('div');
                messageEl.className = `message ${type}`;
                messageEl.textContent = message;
                
                // 添加到页面
                document.body.appendChild(messageEl);
                
                // 自动消失
                setTimeout(() => {
                    messageEl.classList.add('fade-out');
                    setTimeout(() => {
                        messageEl.remove();
                    }, 300);
                }, 3000);
            }
        });
    </script>
</body>
</html> 