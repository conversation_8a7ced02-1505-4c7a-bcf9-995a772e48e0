<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>永昌校友系统 - 验证失败</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(to bottom, #3070FA, #6A5EDF);
            color: #fff;
            line-height: 1.4;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            padding: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        header {
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 24px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        }

        h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .content {
            background-color: #fff;
            border-radius: 12px;
            padding: 20px;
            margin: 0 auto;
            max-width: 400px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            color: #f44336;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-title svg {
            margin-right: 8px;
        }

        .error-list {
            margin-bottom: 30px;
        }

        .error-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            color: #333;
        }

        .error-item svg {
            flex-shrink: 0;
            margin-right: 8px;
            color: #f44336;
            margin-top: 2px;
        }

        .error-item span {
            font-size: 14px;
        }

        .back-btn {
            background-color: #3070FA;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 12px 0;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
            text-align: center;
            text-decoration: none;
            display: block;
        }

        .back-btn:hover {
            background-color: #2861e0;
        }

        footer {
            margin-top: auto;
            text-align: center;
            font-size: 12px;
            opacity: 0.7;
            padding: 8px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>永昌校友系统</h1>
        </header>

        <div class="content">
            <!-- 移除了标题和图标部分 -->

            <div class="error-list" id="errorList">
                <!-- 错误信息将动态插入 -->
            </div>

            <a href="register.html" class="back-btn">返回修改</a>
        </div>

        <footer>
            <p>永昌校友系统 © 2025</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL参数中的错误信息
            const urlParams = new URLSearchParams(window.location.search);
            const errors = urlParams.get('errors');
            
            if (errors) {
                // 解析JSON错误数组
                try {
                    const errorArray = JSON.parse(decodeURIComponent(errors));
                    const errorList = document.getElementById('errorList');
                    
                    // 清空错误列表
                    errorList.innerHTML = '';
                    
                    // 添加每个错误项
                    errorArray.forEach(error => {
                        const errorItem = document.createElement('div');
                        errorItem.className = 'error-item';
                        errorItem.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <span>${error}</span>
                        `;
                        errorList.appendChild(errorItem);
                    });
                } catch (e) {
                    console.error('解析错误信息失败:', e);
                }
            } else {
                // 如果没有错误参数，显示一个默认错误信息
                const errorList = document.getElementById('errorList');
                errorList.innerHTML = `
                    <div class="error-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <span>身份信息验证失败，请确认信息正确性或联系工作人员</span>
                    </div>
                `;
            }
        });
    </script>
</body>
</html> 