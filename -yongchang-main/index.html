<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>永昌校友系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(to bottom, #3070FA, #6A5EDF);
            color: #fff;
            line-height: 1.4;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .welcome-text {
            margin-top: 180px;
            margin-bottom: 20px;
            padding: 0 15px;
        }

        .welcome-text h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .welcome-text p {
            font-size: 16px;
            opacity: 0.9;
        }

        .login-card {
            background-color: white;
            border-radius: 12px;
            padding: 25px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: #333;
            font-size: 18px;
            text-align: center;
            margin-bottom: 25px;
            font-weight: 600;
        }

        .input-group {
            position: relative;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 25px;
            padding: 3px;
        }

        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;
            color: #999;
        }

        input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 12px 10px;
            font-size: 15px;
            outline: none;
            color: #333;
        }

        input::placeholder {
            color: #aaa;
        }

        .get-code-btn {
            background-color: #3070FA;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            white-space: nowrap;
            position: absolute;
            right: 5px;
        }

        .login-btn {
            width: 100%;
            background-color: #3070FA;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 13px 0;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 20px;
        }

        .register-link {
            margin-top: 15px;
            text-align: center;
            font-size: 14px;
            color: #333;
        }

        .register-link a {
            color: #3070FA;
            text-decoration: none;
            font-weight: 500;
        }

        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="welcome-text">
            <h1>您好！</h1>
            <p>欢迎来到永昌校友系统</p>
        </div>

        <div class="login-card">
            <h2>永昌校友登录</h2>
            
            <form id="loginForm">
                <div class="input-group">
                    <div class="icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                            <line x1="12" y1="18" x2="12" y2="18"></line>
                        </svg>
                    </div>
                    <input type="tel" id="phone" placeholder="请输入手机号码" required>
                </div>
                
                <div class="input-group">
                    <div class="icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                    </div>
                    <input type="text" id="code" placeholder="请输入验证码" required style="padding-right: 100px;">
                    <button type="button" id="getCodeBtn" class="get-code-btn">获取验证码</button>
                </div>
                
                <button type="submit" class="login-btn">登录</button>
                
                <div class="register-link">
                    <span>没有账号？</span>
                    <a href="register.html" id="registerBtn">立即注册</a>
                </div>
            </form>
        </div>
        
        <div class="floating-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
                <circle cx="12" cy="12" r="2"/>
            </svg>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const phoneInput = document.getElementById('phone');
            const codeInput = document.getElementById('code');
            const getCodeBtn = document.getElementById('getCodeBtn');
            const registerBtn = document.getElementById('registerBtn');
            
            // 获取验证码按钮点击事件
            getCodeBtn.addEventListener('click', function() {
                const phone = phoneInput.value.trim();
                
                if (!phone) {
                    showMessage('请输入手机号码', 'error');
                    return;
                }
                
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    showMessage('请输入有效的手机号码', 'error');
                    return;
                }
                
                // 模拟发送验证码
                let countdown = 60;
                getCodeBtn.disabled = true;
                getCodeBtn.textContent = `${countdown}秒后重试`;
                
                const timer = setInterval(() => {
                    countdown--;
                    getCodeBtn.textContent = `${countdown}秒后重试`;
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        getCodeBtn.disabled = false;
                        getCodeBtn.textContent = '获取验证码';
                    }
                }, 1000);
                
                showMessage('验证码已发送', 'success');
            });
            
            // 登录表单提交事件
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const phone = phoneInput.value.trim();
                const code = codeInput.value.trim();
                
                if (!phone) {
                    showMessage('请输入手机号码', 'error');
                    return;
                }
                
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    showMessage('请输入有效的手机号码', 'error');
                    return;
                }
                
                if (!code) {
                    showMessage('请输入验证码', 'error');
                    return;
                }
                
                // 模拟登录请求
                const loginBtn = document.querySelector('.login-btn');
                const originalText = loginBtn.textContent;
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
                
                setTimeout(() => {
                    // 登录成功
                    showMessage('登录成功', 'success');
                    
                    // 跳转到首页
                    setTimeout(() => {
                        window.location.href = 'home.html';
                    }, 1500);
                    
                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    loginBtn.textContent = originalText;
                }, 1500);
            });
            
            // 注册按钮点击事件
            registerBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = 'register.html';
            });
            
            // 显示消息提示
            function showMessage(message, type) {
                // 移除已有消息
                const oldMessage = document.querySelector('.message');
                if (oldMessage) {
                    oldMessage.remove();
                }
                
                // 创建新消息
                const messageEl = document.createElement('div');
                messageEl.className = `message ${type}`;
                messageEl.textContent = message;
                messageEl.style.position = 'fixed';
                messageEl.style.top = '50%';
                messageEl.style.left = '50%';
                messageEl.style.transform = 'translate(-50%, -50%)';
                messageEl.style.padding = '10px 16px';
                messageEl.style.borderRadius = '4px';
                messageEl.style.color = 'white';
                messageEl.style.fontSize = '14px';
                messageEl.style.zIndex = '1000';
                messageEl.style.maxWidth = '80%';
                messageEl.style.textAlign = 'center';
                
                if (type === 'error') {
                    messageEl.style.backgroundColor = 'rgba(244, 67, 54, 0.9)';
                } else {
                    messageEl.style.backgroundColor = 'rgba(48, 112, 250, 0.9)';
                }
                
                // 添加到页面
                document.body.appendChild(messageEl);
                
                // 自动消失
                setTimeout(() => {
                    messageEl.style.opacity = '0';
                    messageEl.style.transition = 'opacity 0.3s';
                    setTimeout(() => {
                        messageEl.remove();
                    }, 300);
                }, 3000);
            }
        });
    </script>
</body>
</html> 